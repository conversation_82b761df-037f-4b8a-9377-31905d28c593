# PaoLife Windows平台打包部署方案

## 概述

本文档详细说明PaoLife Electron应用程序在Windows平台的打包部署流程，包括安装包制作、便携版创建、构建工具配置等。

## 1. 开发环境图标问题解决

### 1.1 问题分析

在开发模式下Windows任务栏显示Electron默认图标的原因：
- 原配置只为Linux平台设置图标：`...(process.platform === 'linux' ? { icon } : {})`
- Windows和macOS平台没有设置图标属性

### 1.2 解决方案

已修复主进程配置 (`src/main/index.ts`)：
```typescript
mainWindow = new BrowserWindow({
  width: 1920,
  height: 1080,
  frame: false,
  show: false,
  autoHideMenuBar: true,
  icon: icon, // 为所有平台设置图标
  webPreferences: {
    preload: join(__dirname, '../preload/index.js'),
    sandbox: false,
    webSecurity: !is.dev,
    allowRunningInsecureContent: is.dev
  }
})
```

重启开发服务器后，Windows任务栏将显示正确的应用图标。

## 2. 构建工具配置

### 2.1 当前构建配置

#### package.json 构建脚本
```json
{
  "scripts": {
    "build": "npm run typecheck && prisma generate && electron-vite build",
    "build:unpack": "npm run build && electron-builder --dir",
    "build:win": "npm run build && electron-builder --win",
    "postinstall": "electron-builder install-app-deps && prisma generate"
  }
}
```

#### electron-builder.yml 配置

```yaml
appId: com.electron.app
productName: paolife
directories:
  buildResources: build

# Windows特定配置
win:
  executableName: paolife

# NSIS安装程序配置
nsis:
  artifactName: ${name}-${version}-setup.${ext}
  shortcutName: ${productName}
  uninstallDisplayName: ${productName}
  createDesktopShortcut: always

# 发布配置
publish:
  provider: generic
  url: https://example.com/auto-updates

# Electron下载镜像
electronDownload:
  mirror: https://npmmirror.com/mirrors/electron/
```

### 2.2 构建环境要求

#### 系统要求
- **操作系统**: Windows 10/11 或 Windows Server 2016+
- **Node.js**: 18.x 或更高版本
- **包管理器**: pnpm (推荐) 或 npm
- **Python**: 3.x (用于原生模块编译)
- **Visual Studio Build Tools**: 2019或更高版本

#### 安装构建工具
```powershell
# 安装Node.js (使用Chocolatey)
choco install nodejs

# 安装pnpm
npm install -g pnpm

# 安装Windows构建工具
npm install -g windows-build-tools

# 或者安装Visual Studio Build Tools
# 下载并安装: https://visualstudio.microsoft.com/downloads/#build-tools-for-visual-studio-2022
```

## 3. Windows安装包制作

### 3.1 NSIS安装程序

#### 基础配置
```yaml
# electron-builder.yml
win:
  executableName: paolife
  target:
    - target: nsis
      arch: [x64, ia32]  # 支持64位和32位
  icon: build/icon.ico
  requestedExecutionLevel: asInvoker

nsis:
  oneClick: false                    # 允许用户选择安装路径
  allowElevation: true              # 允许提升权限
  allowToChangeInstallationDirectory: true
  installerIcon: build/icon.ico     # 安装程序图标
  uninstallerIcon: build/icon.ico   # 卸载程序图标
  installerHeaderIcon: build/icon.ico
  createDesktopShortcut: always     # 创建桌面快捷方式
  createStartMenuShortcut: true     # 创建开始菜单快捷方式
  shortcutName: PaoLife             # 快捷方式名称
  include: build/installer.nsh      # 自定义NSIS脚本
```

#### 自定义安装脚本 (build/installer.nsh)
```nsis
# 自定义安装页面
!macro customInstall
  # 创建卸载信息
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${UNINSTALL_APP_KEY}" "DisplayName" "${PRODUCT_NAME}"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${UNINSTALL_APP_KEY}" "DisplayVersion" "${VERSION}"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${UNINSTALL_APP_KEY}" "Publisher" "${COMPANY_NAME}"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${UNINSTALL_APP_KEY}" "DisplayIcon" "$INSTDIR\${PRODUCT_FILENAME}.exe"
!macroend

# 自定义卸载
!macro customUnInstall
  # 清理注册表
  DeleteRegKey HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${UNINSTALL_APP_KEY}"
  
  # 清理用户数据（可选）
  RMDir /r "$APPDATA\${PRODUCT_NAME}"
!macroend
```

### 3.2 MSI安装包

#### 配置MSI目标
```yaml
# electron-builder.yml
win:
  target:
    - target: nsis
      arch: [x64]
    - target: msi
      arch: [x64]

# MSI特定配置
msi:
  oneClick: false
  upgradeCode: "12345678-1234-1234-1234-123456789012"  # 固定的升级代码
  warningsAsErrors: false
```

### 3.3 构建命令

#### 标准构建
```bash
# 构建Windows安装包
pnpm run build:win

# 构建未打包版本（用于测试）
pnpm run build:unpack

# 构建特定架构
npx electron-builder --win --x64
npx electron-builder --win --ia32
```

#### 高级构建选项
```bash
# 跳过代码签名
npx electron-builder --win --publish=never

# 构建到指定目录
npx electron-builder --win --dir

# 详细输出
npx electron-builder --win --verbose
```

## 4. 便携版（绿色版）制作

### 4.1 配置便携版

#### 添加便携版目标
```yaml
# electron-builder.yml
win:
  target:
    - target: nsis
      arch: [x64]
    - target: portable
      arch: [x64]

# 便携版配置
portable:
  artifactName: ${name}-${version}-portable.${ext}
  requestedExecutionLevel: asInvoker
  unpackDirName: ${productName}
```

### 4.2 便携版特性

#### 数据存储配置
```typescript
// src/main/index.ts
import { app } from 'electron'
import { join } from 'path'

// 便携版数据目录配置
const isPortable = process.env.PORTABLE_EXECUTABLE_DIR !== undefined
const userDataPath = isPortable 
  ? join(process.env.PORTABLE_EXECUTABLE_DIR!, 'Data')
  : app.getPath('userData')

app.setPath('userData', userDataPath)
```

#### 便携版检测
```typescript
// 检测是否为便携版
const isPortable = () => {
  return process.env.PORTABLE_EXECUTABLE_DIR !== undefined ||
         process.argv.includes('--portable')
}

// 设置便携版标识
if (isPortable()) {
  app.setName('PaoLife Portable')
}
```

### 4.3 便携版构建

```bash
# 构建便携版
npx electron-builder --win --portable

# 构建所有Windows目标
npx electron-builder --win --nsis --portable
```

## 5. 代码签名配置

### 5.1 证书准备

#### 获取代码签名证书
- **商业证书**: 从CA机构购买（如DigiCert、Sectigo）
- **EV证书**: 扩展验证证书，提供更高信任度
- **自签名证书**: 仅用于测试环境

#### 证书格式
- **PFX/P12**: Windows标准格式
- **PEM**: 需要私钥和证书文件

### 5.2 签名配置

#### 环境变量配置
```bash
# Windows环境变量
set CSC_LINK=path/to/certificate.p12
set CSC_KEY_PASSWORD=certificate_password

# 或使用.env文件
CSC_LINK=certificate.p12
CSC_KEY_PASSWORD=your_password
```

#### electron-builder配置
```yaml
# electron-builder.yml
win:
  certificateFile: certificate.p12
  certificatePassword: ${env.CSC_KEY_PASSWORD}
  signingHashAlgorithms: [sha256]
  timeStampServer: http://timestamp.digicert.com
  
# 发布时自动签名
publish:
  provider: generic
  url: https://example.com/auto-updates
```

### 5.3 签名验证

```bash
# 验证签名
signtool verify /pa /v "dist/paolife-setup.exe"

# 查看证书信息
signtool verify /pa /v /d "dist/paolife-setup.exe"
```

## 6. 自动更新配置

### 6.1 更新服务器配置

#### 静态文件服务器
```yaml
# electron-builder.yml
publish:
  provider: generic
  url: https://your-domain.com/updates/
  channel: latest
```

#### 更新文件结构
```
updates/
├── latest.yml              # 更新元数据
├── paolife-1.0.0-setup.exe # 安装包
└── paolife-1.0.0-setup.exe.blockmap # 增量更新映射
```

### 6.2 应用内更新逻辑

#### 主进程更新检查
```typescript
// src/main/updater.ts
import { autoUpdater } from 'electron-updater'

// 配置更新服务器
autoUpdater.setFeedURL({
  provider: 'generic',
  url: 'https://your-domain.com/updates/'
})

// 检查更新
export const checkForUpdates = () => {
  if (process.env.NODE_ENV === 'development') {
    return
  }
  
  autoUpdater.checkForUpdatesAndNotify()
}

// 更新事件处理
autoUpdater.on('update-available', () => {
  console.log('Update available')
})

autoUpdater.on('update-downloaded', () => {
  console.log('Update downloaded')
  autoUpdater.quitAndInstall()
})
```

## 7. 构建优化

### 7.1 构建性能优化

#### 并行构建
```yaml
# electron-builder.yml
buildDependenciesFromSource: false
nodeGypRebuild: false
npmRebuild: false
```

#### 缓存配置
```bash
# 使用构建缓存
export ELECTRON_CACHE="C:\electron-cache"
export ELECTRON_BUILDER_CACHE="C:\electron-builder-cache"
```

### 7.2 包大小优化

#### 排除不必要文件
```yaml
# electron-builder.yml
files:
  - '!**/.vscode/*'
  - '!src/*'
  - '!electron.vite.config.{js,ts,mjs,cjs}'
  - '!{.eslintcache,eslint.config.mjs,.prettierignore,.prettierrc.yaml}'
  - '!{.env,.env.*,.npmrc,pnpm-lock.yaml}'
  - '!{tsconfig.json,tsconfig.node.json,tsconfig.web.json}'
  - '!docs/*'
  - '!README.md'
```

#### 压缩配置
```yaml
compression: maximum  # 最大压缩
nsis:
  differentialPackage: true  # 启用增量更新
```

## 8. 部署流程

### 8.1 本地构建部署

```bash
# 1. 安装依赖
pnpm install

# 2. 类型检查
pnpm run typecheck

# 3. 构建应用
pnpm run build:win

# 4. 测试安装包
# 运行 dist/paolife-*-setup.exe
```

### 8.2 CI/CD自动化部署

#### GitHub Actions配置
```yaml
# .github/workflows/build-windows.yml
name: Build Windows

on:
  push:
    tags: ['v*']

jobs:
  build:
    runs-on: windows-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'pnpm'
    
    - name: Install dependencies
      run: pnpm install
    
    - name: Build application
      run: pnpm run build:win
      env:
        CSC_LINK: ${{ secrets.CSC_LINK }}
        CSC_KEY_PASSWORD: ${{ secrets.CSC_KEY_PASSWORD }}
    
    - name: Upload artifacts
      uses: actions/upload-artifact@v3
      with:
        name: windows-installer
        path: dist/*.exe
```

### 8.3 发布检查清单

#### 构建前检查
- [ ] 更新版本号 (package.json)
- [ ] 更新变更日志
- [ ] 检查图标文件
- [ ] 验证证书有效性
- [ ] 测试开发版本

#### 构建后检查
- [ ] 验证安装包签名
- [ ] 测试安装/卸载流程
- [ ] 检查快捷方式创建
- [ ] 验证自动更新功能
- [ ] 测试便携版功能

## 9. 故障排除

### 9.1 常见构建错误

#### 权限错误
```bash
# 错误: EPERM: operation not permitted
# 解决: 以管理员身份运行命令提示符
```

#### 证书错误
```bash
# 错误: Certificate not found
# 解决: 检查证书路径和密码
set CSC_LINK=absolute/path/to/certificate.p12
```

#### 依赖编译错误
```bash
# 错误: node-gyp rebuild failed
# 解决: 安装Visual Studio Build Tools
npm install -g windows-build-tools
```

### 9.2 运行时问题

#### 应用无法启动
- 检查依赖项是否完整
- 验证.NET Framework版本
- 查看Windows事件日志

#### 图标显示问题
- 确认图标文件格式正确
- 清理图标缓存
- 重启Windows资源管理器

---

通过遵循本方案，您可以成功构建和部署PaoLife应用程序的Windows版本，包括标准安装包和便携版，确保在Windows系统上的良好用户体验。
