import React, { useState, useEffect } from 'react'
import { Outlet, useNavigate } from 'react-router-dom'
import { useGlobalShortcuts } from '../../hooks/useGlobalShortcuts'
import { ShortcutHelpPanel } from '../features/ShortcutHelpPanel'
import { ExitConfirmDialog, useExitConfirmation } from '../dialogs/ExitConfirmDialog'

export function AppWithShortcuts() {
  const [showShortcutHelp, setShowShortcutHelp] = useState(false)
  const navigate = useNavigate()

  // 初始化退出确认
  const { showExitDialog, handleConfirmExit, handleCancelExit } = useExitConfirmation()

  // 在Router上下文内启用全局快捷键
  useGlobalShortcuts()

  // 监听快捷键帮助事件
  useEffect(() => {
    const handleOpenShortcutHelp = () => {
      setShowShortcutHelp(true)
    }

    const handleCloseDialog = () => {
      setShowShortcutHelp(false)
    }

    // 监听全局导航事件
    const handleGlobalNavigate = (_, path: string) => {
      console.log('🔧 [DEBUG] Global navigation triggered:', path)
      navigate(path)
    }

    document.addEventListener('open-shortcut-help', handleOpenShortcutHelp)
    document.addEventListener('close-dialog', handleCloseDialog)

    // 监听来自主进程的全局导航事件
    if (window.electronAPI?.ipcRenderer) {
      window.electronAPI.ipcRenderer.on('global-navigate', handleGlobalNavigate)
    }

    return () => {
      document.removeEventListener('open-shortcut-help', handleOpenShortcutHelp)
      document.removeEventListener('close-dialog', handleCloseDialog)
      if (window.electronAPI?.ipcRenderer) {
        window.electronAPI.ipcRenderer.removeAllListeners('global-navigate')
      }
    }
  }, [])

  return (
    <>
      <Outlet />
      {/* 快捷键帮助面板 */}
      <ShortcutHelpPanel
        open={showShortcutHelp}
        onOpenChange={setShowShortcutHelp}
      />
      {/* 退出确认对话框 */}
      <ExitConfirmDialog
        isOpen={showExitDialog}
        onConfirm={handleConfirmExit}
        onCancel={handleCancelExit}
      />
    </>
  )
}
