import { PrismaClient } from '@prisma/client'
import { app } from 'electron'
import path from 'path'
import fs from 'fs'

/**
 * Database initializer for production environment
 * Creates tables and applies schema when database doesn't exist
 */
export class DatabaseInitializer {
  private prisma: PrismaClient

  constructor(prisma: PrismaClient) {
    this.prisma = prisma
  }

  /**
   * Check if database has been initialized (has tables)
   */
  async isDatabaseInitialized(): Promise<boolean> {
    try {
      // Try to query a simple table to check if schema exists
      await this.prisma.$queryRaw`SELECT name FROM sqlite_master WHERE type='table' AND name='User' LIMIT 1`
      return true
    } catch (error) {
      console.log('Database not initialized, will create schema')
      return false
    }
  }

  /**
   * Initialize database schema in production
   */
  async initializeSchema(): Promise<{ success: boolean; error?: string }> {
    try {
      console.log('Initializing database schema...')

      // Get schema path from packaged app
      const isDevelopment = process.env.NODE_ENV === 'development' || !app.isPackaged
      let schemaPath: string

      if (isDevelopment) {
        schemaPath = path.join(process.cwd(), 'prisma', 'schema.prisma')
      } else {
        // In production, schema is in resources
        const resourcesPath = process.resourcesPath
        const possibleSchemaPaths = [
          path.join(resourcesPath, 'app', 'prisma', 'schema.prisma'),
          path.join(app.getAppPath(), 'prisma', 'schema.prisma'),
          path.join(process.cwd(), 'prisma', 'schema.prisma')
        ]

        schemaPath = ''
        for (const possiblePath of possibleSchemaPaths) {
          if (fs.existsSync(possiblePath)) {
            schemaPath = possiblePath
            console.log(`Found schema at: ${schemaPath}`)
            break
          }
        }

        if (!schemaPath) {
          throw new Error('Schema file not found in packaged app')
        }
      }

      // Execute the schema creation SQL directly
      await this.createTables()

      console.log('Database schema initialized successfully')
      return { success: true }

    } catch (error) {
      console.error('Failed to initialize database schema:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Create all tables using raw SQL
   */
  private async createTables(): Promise<void> {
    console.log('Creating database tables...')

    // Create tables in dependency order
    const createTableQueries = [
      // User table
      `CREATE TABLE IF NOT EXISTS "User" (
        "id" TEXT NOT NULL PRIMARY KEY,
        "username" TEXT NOT NULL UNIQUE,
        "password" TEXT NOT NULL,
        "settings" TEXT
      )`,

      // Area table
      `CREATE TABLE IF NOT EXISTS "Area" (
        "id" TEXT NOT NULL PRIMARY KEY,
        "name" TEXT NOT NULL,
        "description" TEXT,
        "color" TEXT,
        "icon" TEXT,
        "archived" BOOLEAN NOT NULL DEFAULT false,
        "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        "updatedAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
      )`,

      // Project table
      `CREATE TABLE IF NOT EXISTS "Project" (
        "id" TEXT NOT NULL PRIMARY KEY,
        "name" TEXT NOT NULL,
        "description" TEXT,
        "status" TEXT NOT NULL DEFAULT 'planning',
        "priority" TEXT NOT NULL DEFAULT 'medium',
        "startDate" DATETIME,
        "endDate" DATETIME,
        "archived" BOOLEAN NOT NULL DEFAULT false,
        "areaId" TEXT,
        "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        "updatedAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY ("areaId") REFERENCES "Area" ("id") ON DELETE SET NULL ON UPDATE CASCADE
      )`,

      // Task table
      `CREATE TABLE IF NOT EXISTS "Task" (
        "id" TEXT NOT NULL PRIMARY KEY,
        "title" TEXT NOT NULL,
        "description" TEXT,
        "status" TEXT NOT NULL DEFAULT 'todo',
        "priority" TEXT NOT NULL DEFAULT 'medium',
        "dueDate" DATETIME,
        "completedAt" DATETIME,
        "projectId" TEXT,
        "areaId" TEXT,
        "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        "updatedAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY ("projectId") REFERENCES "Project" ("id") ON DELETE SET NULL ON UPDATE CASCADE,
        FOREIGN KEY ("areaId") REFERENCES "Area" ("id") ON DELETE SET NULL ON UPDATE CASCADE
      )`,

      // ResourceLink table
      `CREATE TABLE IF NOT EXISTS "ResourceLink" (
        "id" TEXT NOT NULL PRIMARY KEY,
        "title" TEXT NOT NULL,
        "url" TEXT NOT NULL,
        "description" TEXT,
        "type" TEXT NOT NULL DEFAULT 'link',
        "projectId" TEXT,
        "areaId" TEXT,
        "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        "updatedAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY ("projectId") REFERENCES "Project" ("id") ON DELETE SET NULL ON UPDATE CASCADE,
        FOREIGN KEY ("areaId") REFERENCES "Area" ("id") ON DELETE SET NULL ON UPDATE CASCADE
      )`,

      // Habit table
      `CREATE TABLE IF NOT EXISTS "Habit" (
        "id" TEXT NOT NULL PRIMARY KEY,
        "name" TEXT NOT NULL,
        "description" TEXT,
        "frequency" TEXT NOT NULL DEFAULT 'daily',
        "targetValue" INTEGER,
        "unit" TEXT,
        "areaId" TEXT,
        "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        "updatedAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY ("areaId") REFERENCES "Area" ("id") ON DELETE SET NULL ON UPDATE CASCADE
      )`,

      // AreaMetric table
      `CREATE TABLE IF NOT EXISTS "AreaMetric" (
        "id" TEXT NOT NULL PRIMARY KEY,
        "name" TEXT NOT NULL,
        "description" TEXT,
        "unit" TEXT,
        "targetValue" REAL,
        "currentValue" REAL NOT NULL DEFAULT 0,
        "trackingType" TEXT NOT NULL DEFAULT 'manual',
        "direction" TEXT NOT NULL DEFAULT 'higher_better',
        "areaId" TEXT NOT NULL,
        "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        "updatedAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY ("areaId") REFERENCES "Area" ("id") ON DELETE CASCADE ON UPDATE CASCADE
      )`,

      // Checklist table
      `CREATE TABLE IF NOT EXISTS "Checklist" (
        "id" TEXT NOT NULL PRIMARY KEY,
        "name" TEXT NOT NULL,
        "description" TEXT,
        "items" TEXT NOT NULL,
        "areaId" TEXT,
        "projectId" TEXT,
        "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        "updatedAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY ("areaId") REFERENCES "Area" ("id") ON DELETE SET NULL ON UPDATE CASCADE,
        FOREIGN KEY ("projectId") REFERENCES "Project" ("id") ON DELETE SET NULL ON UPDATE CASCADE
      )`,

      // RecurringTask table
      `CREATE TABLE IF NOT EXISTS "RecurringTask" (
        "id" TEXT NOT NULL PRIMARY KEY,
        "title" TEXT NOT NULL,
        "description" TEXT,
        "frequency" TEXT NOT NULL DEFAULT 'weekly',
        "nextDueDate" DATETIME,
        "lastCompletedAt" DATETIME,
        "areaId" TEXT,
        "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        "updatedAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY ("areaId") REFERENCES "Area" ("id") ON DELETE SET NULL ON UPDATE CASCADE
      )`,

      // Create indexes for better performance
      `CREATE INDEX IF NOT EXISTS "Project_areaId_idx" ON "Project"("areaId")`,
      `CREATE INDEX IF NOT EXISTS "Task_projectId_idx" ON "Task"("projectId")`,
      `CREATE INDEX IF NOT EXISTS "Task_areaId_idx" ON "Task"("areaId")`,
      `CREATE INDEX IF NOT EXISTS "ResourceLink_projectId_idx" ON "ResourceLink"("projectId")`,
      `CREATE INDEX IF NOT EXISTS "ResourceLink_areaId_idx" ON "ResourceLink"("areaId")`,
      `CREATE INDEX IF NOT EXISTS "AreaMetric_areaId_idx" ON "AreaMetric"("areaId")`,
      `CREATE INDEX IF NOT EXISTS "Habit_areaId_idx" ON "Habit"("areaId")`,
      `CREATE INDEX IF NOT EXISTS "Checklist_areaId_idx" ON "Checklist"("areaId")`,
      `CREATE INDEX IF NOT EXISTS "Checklist_projectId_idx" ON "Checklist"("projectId")`,
      `CREATE INDEX IF NOT EXISTS "RecurringTask_areaId_idx" ON "RecurringTask"("areaId")`
    ]

    // Execute each query
    for (const query of createTableQueries) {
      try {
        await this.prisma.$executeRawUnsafe(query)
        console.log('✓ Table created successfully')
      } catch (error) {
        console.error('Failed to create table:', error)
        throw error
      }
    }

    console.log('All tables created successfully')
  }

  /**
   * Verify database integrity
   */
  async verifyDatabase(): Promise<{ success: boolean; tables: string[]; error?: string }> {
    try {
      const tables = await this.prisma.$queryRaw<Array<{ name: string }>>`
        SELECT name FROM sqlite_master WHERE type='table' ORDER BY name
      `

      const tableNames = tables.map(t => t.name).filter(name => !name.startsWith('sqlite_'))

      console.log('Database tables found:', tableNames)

      return {
        success: true,
        tables: tableNames
      }
    } catch (error) {
      return {
        success: false,
        tables: [],
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }
}
