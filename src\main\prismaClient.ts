import { app } from 'electron'
import path from 'path'
import fs from 'fs'

/**
 * Custom Prisma client loader for packaged applications
 */
export async function createPrismaClient() {
  const isDevelopment = process.env.NODE_ENV === 'development' || !app.isPackaged

  // Try normal import first (works in both dev and production with proper packaging)
  try {
    const { PrismaClient } = await import('@prisma/client')
    console.log('Successfully imported PrismaClient using standard import')
    return new PrismaClient()
  } catch (importError) {
    console.warn('Standard import failed, trying custom loading:', importError)
  }

  if (isDevelopment) {
    throw new Error('Failed to load PrismaClient in development mode')
  }
  
  // In production, handle packaged environment
  console.log('Loading Prisma client for packaged app...')
  
  // Setup paths for packaged app
  const resourcesPath = process.resourcesPath
  const appPath = app.getAppPath()
  
  // Possible locations for Prisma client (including pnpm paths)
  const possibleClientPaths = [
    path.join(resourcesPath, 'app', 'node_modules', '.prisma', 'client'),
    path.join(appPath, 'node_modules', '.prisma', 'client'),
    path.join(process.cwd(), 'node_modules', '.prisma', 'client'),
    path.join(resourcesPath, 'node_modules', '.prisma', 'client'),
    // pnpm paths
    path.join(resourcesPath, 'app', 'node_modules', '.pnpm', '@prisma+client@6.12.0_prism_852c2f508fa5d5c04099a9cee124d4df', 'node_modules', '@prisma', 'client'),
    path.join(appPath, 'node_modules', '.pnpm', '@prisma+client@6.12.0_prism_852c2f508fa5d5c04099a9cee124d4df', 'node_modules', '@prisma', 'client'),
    path.join(process.cwd(), 'node_modules', '.pnpm', '@prisma+client@6.12.0_prism_852c2f508fa5d5c04099a9cee124d4df', 'node_modules', '@prisma', 'client'),
    // Generic pnpm pattern search
    path.join(process.cwd(), 'node_modules', '@prisma', 'client')
  ]

  // Possible locations for engines (including pnpm paths)
  const possibleEnginesPaths = [
    path.join(resourcesPath, 'app', 'node_modules', '@prisma', 'engines'),
    path.join(appPath, 'node_modules', '@prisma', 'engines'),
    path.join(process.cwd(), 'node_modules', '@prisma', 'engines'),
    path.join(resourcesPath, 'node_modules', '@prisma', 'engines'),
    // pnpm paths
    path.join(resourcesPath, 'app', 'node_modules', '.pnpm', '@prisma+engines@6.12.0', 'node_modules', '@prisma', 'engines'),
    path.join(appPath, 'node_modules', '.pnpm', '@prisma+engines@6.12.0', 'node_modules', '@prisma', 'engines'),
    path.join(process.cwd(), 'node_modules', '.pnpm', '@prisma+engines@6.12.0', 'node_modules', '@prisma', 'engines')
  ]
  
  // Find client path
  let clientPath: string | null = null
  for (const possiblePath of possibleClientPaths) {
    if (fs.existsSync(possiblePath)) {
      clientPath = possiblePath
      console.log(`Found Prisma client at: ${clientPath}`)
      break
    }
  }
  
  // Find engines path
  let enginesPath: string | null = null
  for (const possiblePath of possibleEnginesPaths) {
    if (fs.existsSync(possiblePath)) {
      enginesPath = possiblePath
      console.log(`Found Prisma engines at: ${enginesPath}`)
      break
    }
  }
  
  if (!clientPath) {
    throw new Error('Prisma client not found in packaged app')
  }
  
  // Set engine environment variables
  const engineFiles = [
    'query_engine-windows.dll.node',
    'libquery_engine-windows.dll.node'
  ]
  
  for (const engineFile of engineFiles) {
    // Check in client directory first
    let enginePath = path.join(clientPath, engineFile)
    if (fs.existsSync(enginePath)) {
      process.env.PRISMA_QUERY_ENGINE_LIBRARY = enginePath
      console.log(`Set PRISMA_QUERY_ENGINE_LIBRARY: ${enginePath}`)
      break
    }
    
    // Check in engines directory
    if (enginesPath) {
      enginePath = path.join(enginesPath, engineFile)
      if (fs.existsSync(enginePath)) {
        process.env.PRISMA_QUERY_ENGINE_LIBRARY = enginePath
        console.log(`Set PRISMA_QUERY_ENGINE_LIBRARY: ${enginePath}`)
        break
      }
    }
  }
  
  // Try to load the client from the found path
  try {
    // Add the client path to module paths
    const Module = require('module')
    const originalResolveFilename = Module._resolveFilename
    
    Module._resolveFilename = function(request: string, parent: any, isMain: boolean) {
      // Intercept Prisma client requests
      if (request === '@prisma/client' || request === '.prisma/client' || request.includes('.prisma/client')) {
        const clientIndexPath = path.join(clientPath!, 'index.js')
        if (fs.existsSync(clientIndexPath)) {
          return clientIndexPath
        }
      }
      
      return originalResolveFilename.call(this, request, parent, isMain)
    }
    
    // Load the client
    const clientIndexPath = path.join(clientPath, 'index.js')
    const clientModule = require(clientIndexPath)
    const PrismaClient = clientModule.PrismaClient || clientModule.default?.PrismaClient
    
    if (!PrismaClient) {
      throw new Error('PrismaClient not found in loaded module')
    }
    
    console.log('Successfully loaded Prisma client from packaged app')
    return new PrismaClient()
    
  } catch (error) {
    console.error('Failed to load Prisma client from packaged app:', error)
    
    // Fallback: try normal import
    try {
      const { PrismaClient } = await import('@prisma/client')
      return new PrismaClient()
    } catch (fallbackError) {
      console.error('Fallback import also failed:', fallbackError)
      throw new Error(`Failed to load Prisma client: ${error}`)
    }
  }
}
