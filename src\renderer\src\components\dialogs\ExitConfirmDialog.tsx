import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, Dialog<PERSON>eader, DialogTitle, DialogFooter } from '../ui/dialog'
import { Button } from '../ui/button'
import { Checkbox } from '../ui/checkbox'
import { useLanguage } from '../../contexts/LanguageContext'

interface ExitConfirmDialogProps {
  isOpen: boolean
  onConfirm: () => void
  onCancel: () => void
}

export function ExitConfirmDialog({ isOpen, onConfirm, onCancel }: ExitConfirmDialogProps) {
  const { t } = useLanguage()
  const [dontShowAgain, setDontShowAgain] = useState(false)

  // Load saved preference
  useEffect(() => {
    const saved = localStorage.getItem('paolife-exit-confirm-disabled')
    if (saved === 'true') {
      // If user previously chose not to show again, auto-confirm
      if (isOpen) {
        onConfirm()
      }
    }
  }, [isO<PERSON>, onConfirm])

  const handleConfirm = () => {
    if (dontShowAgain) {
      localStorage.setItem('paolife-exit-confirm-disabled', 'true')
    }
    onConfirm()
  }

  const handleCancel = () => {
    onCancel()
  }

  // Don't render if user has disabled confirmations
  const isDisabled = localStorage.getItem('paolife-exit-confirm-disabled') === 'true'
  if (isDisabled) {
    return null
  }

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onCancel()}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <svg 
              className="w-5 h-5 text-amber-500" 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" 
              />
            </svg>
            {t('dialogs.exitConfirm.title', '确认退出')}
          </DialogTitle>
        </DialogHeader>
        
        <div className="py-4">
          <p className="text-sm text-muted-foreground mb-4">
            {t('dialogs.exitConfirm.message', '确定要退出 PaoLife 吗？未保存的更改可能会丢失。')}
          </p>
          
          <div className="flex items-center space-x-2">
            <Checkbox
              id="dont-show-again"
              checked={dontShowAgain}
              onCheckedChange={(checked) => setDontShowAgain(checked as boolean)}
            />
            <label
              htmlFor="dont-show-again"
              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              {t('dialogs.exitConfirm.dontShowAgain', '不再显示此确认')}
            </label>
          </div>
        </div>

        <DialogFooter className="flex gap-2">
          <Button
            variant="outline"
            onClick={handleCancel}
            className="flex-1"
          >
            {t('common.cancel', '取消')}
          </Button>
          <Button
            variant="destructive"
            onClick={handleConfirm}
            className="flex-1"
          >
            {t('dialogs.exitConfirm.confirm', '退出')}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

// Hook to manage exit confirmation
export function useExitConfirmation() {
  const [showExitDialog, setShowExitDialog] = useState(false)

  useEffect(() => {
    const handleCloseRequest = () => {
      const isDisabled = localStorage.getItem('paolife-exit-confirm-disabled') === 'true'
      if (isDisabled) {
        // If confirmations are disabled, quit immediately
        if (window.electronAPI?.window?.forceQuit) {
          window.electronAPI.window.forceQuit()
        }
      } else {
        // Show confirmation dialog
        setShowExitDialog(true)
      }
    }

    // Listen for close request from main process
    if (window.electronAPI?.ipcRenderer) {
      window.electronAPI.ipcRenderer.on('app-close-requested', handleCloseRequest)
    }

    return () => {
      if (window.electronAPI?.ipcRenderer) {
        window.electronAPI.ipcRenderer.removeAllListeners('app-close-requested')
      }
    }
  }, [])

  const handleConfirmExit = () => {
    setShowExitDialog(false)
    if (window.electronAPI?.window?.forceQuit) {
      window.electronAPI.window.forceQuit()
    }
  }

  const handleCancelExit = () => {
    setShowExitDialog(false)
    if (window.electronAPI?.window?.cancelQuit) {
      window.electronAPI.window.cancelQuit()
    }
  }

  return {
    showExitDialog,
    handleConfirmExit,
    handleCancelExit
  }
}
