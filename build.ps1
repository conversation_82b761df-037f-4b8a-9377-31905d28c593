﻿Write-Host "开始构建PaoLife..." -ForegroundColor Green

# 强制结束所有相关进程
Write-Host "结束所有相关进程..." -ForegroundColor Yellow
$processes = @("paolife", "electron", "node")
foreach ($proc in $processes) {
    Get-Process -Name $proc -ErrorAction SilentlyContinue | ForEach-Object {
        Write-Host "结束进程: $($_.ProcessName) (PID: $($_.Id))" -ForegroundColor Red
        Stop-Process -Id $_.Id -Force -ErrorAction SilentlyContinue
    }
}

# 等待进程完全退出
Write-Host "等待进程退出..." -ForegroundColor Yellow
Start-Sleep -Seconds 3

# 清理构建目录
Write-Host "清理构建目录..." -ForegroundColor Yellow
$directories = @("dist_build", "dist", "out")
foreach ($dir in $directories) {
    if (Test-Path $dir) {
        Write-Host "删除目录: $dir" -ForegroundColor Red
        Remove-Item -Path $dir -Recurse -Force -ErrorAction SilentlyContinue
    }
}

# 等待文件系统释放
Start-Sleep -Seconds 1

# 安装依赖
Write-Host "安装依赖..." -ForegroundColor Yellow
pnpm install

# 跳过类型检查，直接构建
Write-Host "跳过类型检查，直接构建..." -ForegroundColor Yellow

# 构建应用
Write-Host "构建应用..." -ForegroundColor Yellow
pnpm run build:win
if ($LASTEXITCODE -ne 0) {
    Write-Host "构建失败！" -ForegroundColor Red
    exit 1
}

Write-Host "构建完成！输出文件在 dist_build 目录" -ForegroundColor Green
Write-Host "便携版文件：dist_build\paolife-1.0.0-portable.exe" -ForegroundColor Cyan