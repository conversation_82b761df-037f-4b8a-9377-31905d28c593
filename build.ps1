﻿Write-Host "开始构建PaoLife..." -ForegroundColor Green

# 清理构建目录
Remove-Item -Path "dist" -Recurse -Force -ErrorAction SilentlyContinue
Remove-Item -Path "out" -Recurse -Force -ErrorAction SilentlyContinue

# 安装依赖
Write-Host "安装依赖..." -ForegroundColor Yellow
pnpm install

# 跳过类型检查，直接构建
Write-Host "跳过类型检查，直接构建..." -ForegroundColor Yellow

# 构建应用
Write-Host "构建应用..." -ForegroundColor Yellow
pnpm run build:win
if ($LASTEXITCODE -ne 0) {
    Write-Host "构建失败！" -ForegroundColor Red
    exit 1
}

Write-Host "构建完成！输出文件在 dist_build 目录" -ForegroundColor Green
Write-Host "便携版文件：dist_build\paolife-1.0.0-portable.exe" -ForegroundColor Cyan