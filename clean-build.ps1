Write-Host "清理构建环境..." -ForegroundColor Green

# 强制结束所有相关进程
Write-Host "结束所有相关进程..." -ForegroundColor Yellow
$processes = @("paolife", "electron", "node")
foreach ($proc in $processes) {
    Get-Process -Name $proc -ErrorAction SilentlyContinue | ForEach-Object {
        Write-Host "结束进程: $($_.ProcessName) (PID: $($_.Id))" -ForegroundColor Red
        Stop-Process -Id $_.Id -Force -ErrorAction SilentlyContinue
    }
}

# 等待进程完全退出
Write-Host "等待进程退出..." -ForegroundColor Yellow
Start-Sleep -Seconds 3

# 清理构建目录
Write-Host "清理构建目录..." -ForegroundColor Yellow
$directories = @("dist_build", "dist", "out")
foreach ($dir in $directories) {
    if (Test-Path $dir) {
        Write-Host "删除目录: $dir" -ForegroundColor Red
        Remove-Item -Path $dir -Recurse -Force -ErrorAction SilentlyContinue
    }
}

# 清理临时文件
Write-Host "清理临时文件..." -ForegroundColor Yellow
Remove-Item -Path "*.log" -Force -ErrorAction SilentlyContinue
Remove-Item -Path "*.tmp" -Force -ErrorAction SilentlyContinue

# 检查文件占用
Write-Host "检查文件占用情况..." -ForegroundColor Yellow
$lockedFiles = @()
$checkPaths = @("dist_build", "dist", "out")
foreach ($path in $checkPaths) {
    if (Test-Path $path) {
        try {
            Get-ChildItem -Path $path -Recurse -ErrorAction Stop | Out-Null
        } catch {
            $lockedFiles += $path
        }
    }
}

if ($lockedFiles.Count -gt 0) {
    Write-Host "警告：以下路径可能仍被占用：" -ForegroundColor Red
    $lockedFiles | ForEach-Object { Write-Host "  - $_" -ForegroundColor Red }
    Write-Host "建议重启 VSCode 或重启计算机后再构建" -ForegroundColor Yellow
} else {
    Write-Host "清理完成，可以开始构建" -ForegroundColor Green
}
