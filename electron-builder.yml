appId: com.codec.paolife
productName: PaoLife
directories:
  buildResources: build
  output: dist
icon: build/icon.png
files:
  - 'out/**/*'
  - 'prisma/schema.prisma'
  - 'node_modules/@prisma/**/*'
  - 'node_modules/.prisma/**/*'
  - 'node_modules/**/*'
  - '!**/.vscode/*'
  - '!src/*'
  - '!electron.vite.config.{js,ts,mjs,cjs}'
  - '!{.eslintcache,eslint.config.mjs,.prettierignore,.prettierrc.yaml,dev-app-update.yml,CHANGELOG.md,README.md}'
  - '!{.env,.env.*,.npmrc,pnpm-lock.yaml}'
  - '!{tsconfig.json,tsconfig.node.json,tsconfig.web.json}'
  - '!docs/*'
  - '!{.gitattributes,.gitignore}'
  - '!.git/'
  - '!README.md'
  - '!node_modules/**/test/**'
  - '!node_modules/**/tests/**'
  - '!node_modules/**/*.md'
  - '!node_modules/**/.github/**'
  - '!prisma/dev.db*'
  - '!prisma/migrations/**'
extraResources:
  - from: node_modules/.prisma/client/
    to: app/node_modules/.prisma/client/
  - from: node_modules/@prisma/client/
    to: app/node_modules/@prisma/client/
  - from: node_modules/@prisma/engines/
    to: app/node_modules/@prisma/engines/
  - from: prisma/schema.prisma
    to: app/prisma/schema.prisma
asarUnpack:
  - resources/**
  - node_modules/@prisma/**
  - node_modules/.prisma/**
  - node_modules/.pnpm/**/@prisma/**
  - node_modules/.pnpm/**/.prisma/**
  - prisma/schema.prisma
includeSubNodeModules: true
compression: maximum
buildDependenciesFromSource: false
nodeGypRebuild: false
npmRebuild: false
win:
  target:
    - target: portable
      arch: [x64]
  executableName: PaoLife
  icon: build/icon.ico
nsis:
  oneClick: false
  allowElevation: true
  allowToChangeInstallationDirectory: true
  installerIcon: build/icon.ico
  uninstallerIcon: build/icon.ico
  installerHeaderIcon: build/icon.ico
  createDesktopShortcut: always
  createStartMenuShortcut: true
  artifactName: ${name}-${version}-setup.${ext}
  shortcutName: ${productName}
  uninstallDisplayName: ${productName}
  differentialPackage: true
  unicode: true
  warningsAsErrors: false
mac:
  icon: build/icon.icns
  entitlementsInherit: build/entitlements.mac.plist
  extendInfo:
    - NSCameraUsageDescription: Application requests access to the device's camera.
    - NSMicrophoneUsageDescription: Application requests access to the device's microphone.
    - NSDocumentsFolderUsageDescription: Application requests access to the user's Documents folder.
    - NSDownloadsFolderUsageDescription: Application requests access to the user's Downloads folder.
  notarize: false
dmg:
  artifactName: ${name}-${version}.${ext}
linux:
  target:
    - AppImage
    - snap
    - deb
  maintainer: electronjs.org
  category: Utility
portable:
  artifactName: ${name}-${version}-portable.${ext}
  requestExecutionLevel: user
  unpackDirName: PaoLife
appImage:
  artifactName: ${name}-${version}.${ext}
publish:
  provider: generic
  url: https://example.com/auto-updates
electronDownload:
  mirror: https://npmmirror.com/mirrors/electron/
