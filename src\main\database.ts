import type { PrismaClient } from '@prisma/client'
import { app } from 'electron'
import path from 'path'
import fs from 'fs'
import type { DatabaseConfig, DatabaseResult } from '../shared/types'
import documentLinkService from './documentLinkService'
import { seedReviewTemplates } from './seedReviewTemplates'
import ReviewDataAggregator from './reviewDataAggregator'
import ReviewAnalyzer from './reviewAnalyzer'
// {{ AURA-X: Add - 导入迁移助手. Approval: 寸止(ID:1738157400). }}
import MigrationHelper from './migrationHelper'
import { createPrismaClient } from './prismaClient'
import { DatabaseInitializer } from './databaseInitializer'

class DatabaseService {
  private prisma: PrismaClient | null = null
  private config: DatabaseConfig | null = null
  private dataAggregator: ReviewDataAggregator | null = null
  private reviewAnalyzer: ReviewAnalyzer | null = null
  // {{ AURA-X: Add - 迁移助手实例. Approval: 寸止(ID:1738157400). }}
  private migrationHelper: MigrationHelper | null = null

  /**
   * Initialize database service with user data directory
   */
  async initialize(): Promise<DatabaseResult<DatabaseConfig>> {
    try {
      // Check if we're in development mode
      const isDevelopment = process.env.NODE_ENV === 'development' || !app.isPackaged

      let databasePath: string
      let userDataPath: string

      if (isDevelopment) {
        // In development, use the project's dev.db file
        userDataPath = process.cwd()
        databasePath = path.join(userDataPath, 'prisma', 'dev.db')
        console.log('Development mode: using project database')
      } else {
        // In production, use user data directory
        userDataPath = app.getPath('userData')
        databasePath = path.join(userDataPath, 'paolife.db')
        console.log('Production mode: using user data directory')
      }

      this.config = {
        databasePath,
        userDataPath
      }

      // Ensure directory exists
      const dbDir = path.dirname(databasePath)
      if (!fs.existsSync(dbDir)) {
        fs.mkdirSync(dbDir, { recursive: true })
      }

      // Update DATABASE_URL to use the correct path (only in production)
      if (!isDevelopment) {
        process.env.DATABASE_URL = `file:${databasePath}`
      }

      // Initialize Prisma client using our custom loader
      this.prisma = await createPrismaClient()

      if (!this.prisma) {
        throw new Error('Failed to create Prisma client')
      }

      // Test connection
      await this.prisma.$connect()

      // Initialize database schema if needed (production only)
      if (!isDevelopment) {
        const initializer = new DatabaseInitializer(this.prisma)
        const isInitialized = await initializer.isDatabaseInitialized()

        if (!isInitialized) {
          console.log('Database not initialized, creating schema...')
          const initResult = await initializer.initializeSchema()

          if (!initResult.success) {
            throw new Error(`Failed to initialize database schema: ${initResult.error}`)
          }

          // Verify database after initialization
          const verifyResult = await initializer.verifyDatabase()
          if (verifyResult.success) {
            console.log('Database verification passed. Tables:', verifyResult.tables)
          } else {
            console.warn('Database verification failed:', verifyResult.error)
          }
        } else {
          console.log('Database already initialized')
        }
      }

      // {{ AURA-X: Add - 初始化迁移助手并检查迁移. Approval: 寸止(ID:1738157400). }}
      this.migrationHelper = new MigrationHelper(this.prisma)

      // 检查并执行必要的迁移
      try {
        // 在开发环境中，强制检查所有迁移
        const isDevelopment = process.env.NODE_ENV === 'development' || !app.isPackaged
        const migrationResult = isDevelopment
          ? await this.migrationHelper.forceCheckAndExecuteMigrations()
          : await this.migrationHelper.checkAndExecuteMigrations()

        if (migrationResult.migrations.length > 0) {
          console.log('已执行迁移:', migrationResult.migrations)
        }
        if (migrationResult.errors.length > 0) {
          console.warn('迁移错误:', migrationResult.errors)
        }
      } catch (migrationError) {
        console.warn('迁移检查失败，但不影响基本功能:', migrationError)
      }

      // Initialize document link service
      documentLinkService.setPrismaClient(this.prisma)

      // Initialize data aggregator and analyzer
      this.dataAggregator = new ReviewDataAggregator(this)
      this.reviewAnalyzer = new ReviewAnalyzer(this)

      // Seed default review templates
      try {
        await seedReviewTemplates(this)
      } catch (seedError) {
        console.warn('Failed to seed review templates:', seedError)
      }

      console.log(`Database initialized successfully at: ${databasePath}`)
      console.log(`Database mode: ${isDevelopment ? 'Development' : 'Production'}`)

      return {
        success: true,
        data: this.config
      }
    } catch (error) {
      console.error('Failed to initialize database:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Get Prisma client instance
   */
  getClient(): PrismaClient {
    if (!this.prisma) {
      throw new Error('Database not initialized. Call initialize() first.')
    }
    return this.prisma
  }

  /**
   * Get database configuration
   */
  getConfig(): DatabaseConfig {
    if (!this.config) {
      throw new Error('Database not initialized. Call initialize() first.')
    }
    return this.config
  }

  /**
   * Get document link service instance
   */
  getDocumentLinkService() {
    return documentLinkService
  }

  /**
   * Close database connection
   */
  async close(): Promise<void> {
    if (this.prisma) {
      await this.prisma.$disconnect()
      this.prisma = null
    }
  }

  /**
   * Test database connection
   */
  async testConnection(): Promise<DatabaseResult<boolean>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      await this.prisma.$queryRaw`SELECT 1`

      return {
        success: true,
        data: true
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Connection test failed'
      }
    }
  }

  /**
   * 获取数据库schema信息（用于调试）
   */
  async getDatabaseInfo(): Promise<DatabaseResult<any>> {
    try {
      if (!this.prisma || !this.migrationHelper) {
        throw new Error('Database not initialized')
      }

      const info = await this.migrationHelper.getDatabaseInfo()
      return {
        success: true,
        data: info
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get database info'
      }
    }
  }

  /**
   * Basic CRUD operations for testing
   */
  async createProject(data: {
    name: string
    description?: string
    goal?: string
    areaId?: string  // {{ AURA-X: Add - 支持创建项目时设置领域关联. Approval: 寸止(ID:1738157400). }}
  }): Promise<DatabaseResult<any>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      const project = await this.prisma.project.create({
        data: {
          name: data.name,
          description: data.description,
          goal: data.goal,
          areaId: data.areaId || null  // {{ AURA-X: Add - 包含领域关联字段. Approval: 寸止(ID:1738157400). }}
        }
      })

      return {
        success: true,
        data: project
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create project'
      }
    }
  }

  async getProjects(): Promise<DatabaseResult<any[]>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      const projects = await this.prisma.project.findMany({
        where: {
          archived: false
        },
        orderBy: {
          createdAt: 'desc'
        }
      })

      return {
        success: true,
        data: projects
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get projects'
      }
    }
  }

  async getProjectById(id: string, includeArchived: boolean = false): Promise<DatabaseResult<any>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      const whereClause: any = { id }
      if (!includeArchived) {
        whereClause.archived = false
      }

      const project = await this.prisma.project.findFirst({
        where: whereClause
      })

      return {
        success: true,
        data: project
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get project by id'
      }
    }
  }

  async deleteProject(id: string): Promise<DatabaseResult<void>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      await this.prisma.project.delete({
        where: { id }
      })

      return {
        success: true
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete project'
      }
    }
  }

  async getArchivedProjects(): Promise<DatabaseResult<any[]>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      const projects = await this.prisma.project.findMany({
        where: {
          archived: true
        },
        orderBy: {
          updatedAt: 'desc'
        }
      })

      return {
        success: true,
        data: projects
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get archived projects'
      }
    }
  }

  async getArchivedAreas(): Promise<DatabaseResult<any[]>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      const areas = await this.prisma.area.findMany({
        where: {
          archived: true
        },
        orderBy: {
          updatedAt: 'desc'
        }
      })

      return {
        success: true,
        data: areas
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get archived areas'
      }
    }
  }

  async archiveProject(id: string): Promise<DatabaseResult<void>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      await this.prisma.project.update({
        where: { id },
        data: { archived: true }
      })

      return {
        success: true
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to archive project'
      }
    }
  }

  async restoreProject(id: string): Promise<DatabaseResult<void>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      await this.prisma.project.update({
        where: { id },
        data: { archived: false }
      })

      return {
        success: true
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to restore project'
      }
    }
  }

  async archiveArea(id: string): Promise<DatabaseResult<void>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      await this.prisma.area.update({
        where: { id },
        data: { archived: true }
      })

      return {
        success: true
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to archive area'
      }
    }
  }

  async restoreArea(id: string): Promise<DatabaseResult<void>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      await this.prisma.area.update({
        where: { id },
        data: { archived: false }
      })

      return {
        success: true
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to restore area'
      }
    }
  }

  async deleteArea(id: string): Promise<DatabaseResult<void>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      await this.prisma.area.delete({
        where: { id }
      })

      return {
        success: true
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete area'
      }
    }
  }

  async getAreas(): Promise<DatabaseResult<any[]>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      const areas = await this.prisma.area.findMany({
        where: {
          archived: false
        },
        orderBy: {
          createdAt: 'desc'
        }
      })

      return {
        success: true,
        data: areas
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get areas'
      }
    }
  }

  async getAreaById(id: string, includeArchived: boolean = false): Promise<DatabaseResult<any>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      const whereClause: any = { id }
      if (!includeArchived) {
        whereClause.archived = false
      }

      const area = await this.prisma.area.findFirst({
        where: whereClause
      })

      return {
        success: true,
        data: area
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get area by id'
      }
    }
  }

  /**
   * ProjectKPI operations
   */
  async createProjectKPI(data: {
    projectId: string
    name: string
    value: string
    target?: string
    unit?: string
    frequency?: string
    // {{ AURA-X: Add - 添加direction字段支持. Approval: 寸止(ID:1738157400). }}
    direction?: string
  }): Promise<DatabaseResult<any>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      const kpi = await this.prisma.projectKPI.create({
        data: {
          projectId: data.projectId,
          name: data.name,
          value: data.value,
          target: data.target,
          unit: data.unit,
          frequency: data.frequency,
          // {{ AURA-X: Add - 包含direction字段. Approval: 寸止(ID:1738157400). }}
          direction: data.direction || 'increase'
        }
      })

      return {
        success: true,
        data: kpi
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create project KPI'
      }
    }
  }

  async getProjectKPIs(projectId: string): Promise<DatabaseResult<any[]>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      const kpis = await this.prisma.projectKPI.findMany({
        where: {
          projectId: projectId
        },
        orderBy: {
          updatedAt: 'desc'
        }
      })

      return {
        success: true,
        data: kpis
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get project KPIs'
      }
    }
  }

  async updateProjectKPI(data: {
    id: string
    updates: {
      name?: string
      value?: string
      target?: string
      unit?: string
      frequency?: string
      // {{ AURA-X: Add - 添加direction字段支持. Approval: 寸止(ID:1738157400). }}
      direction?: string
    }
  }): Promise<DatabaseResult<any>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      const kpi = await this.prisma.projectKPI.update({
        where: { id: data.id },
        data: {
          ...data.updates,
          updatedAt: new Date()
        }
      })

      return {
        success: true,
        data: kpi
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update project KPI'
      }
    }
  }

  async deleteProjectKPI(id: string): Promise<DatabaseResult<void>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      await this.prisma.projectKPI.delete({
        where: { id }
      })

      return {
        success: true
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete project KPI'
      }
    }
  }

  /**
   * KPI Record operations
   */
  async createKPIRecord(data: {
    kpiId: string
    value: string
    note?: string
    recordedAt?: Date
  }): Promise<DatabaseResult<any>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      const record = await this.prisma.kPIRecord.create({
        data: {
          kpiId: data.kpiId,
          value: data.value,
          note: data.note,
          recordedAt: data.recordedAt || new Date()
        }
      })

      // 同时更新KPI的当前值
      await this.prisma.projectKPI.update({
        where: { id: data.kpiId },
        data: {
          value: data.value,
          updatedAt: new Date()
        }
      })

      return {
        success: true,
        data: record
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create KPI record'
      }
    }
  }

  async getKPIRecords(kpiId: string, limit?: number): Promise<DatabaseResult<any[]>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      const records = await this.prisma.kPIRecord.findMany({
        where: { kpiId },
        orderBy: { recordedAt: 'desc' },
        take: limit || 50
      })

      return {
        success: true,
        data: records
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch KPI records'
      }
    }
  }

  async updateKPIRecord(data: {
    id: string
    updates: {
      value?: string
      note?: string
      recordedAt?: Date
    }
  }): Promise<DatabaseResult<any>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      const record = await this.prisma.kPIRecord.update({
        where: { id: data.id },
        data: data.updates
      })

      return {
        success: true,
        data: record
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update KPI record'
      }
    }
  }

  async deleteKPIRecord(id: string): Promise<DatabaseResult<void>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      await this.prisma.kPIRecord.delete({
        where: { id }
      })

      return {
        success: true
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete KPI record'
      }
    }
  }

  /**
   * Area Metric operations
   */
  async createAreaMetric(data: {
    areaId: string
    name: string
    value: string
    target?: string
    unit?: string
    frequency?: string
    // {{ AURA-X: Add - 扩展创建参数支持新字段. Approval: 寸止(ID:1738157400). }}
    trackingType?: string
    habitConfig?: any
    standardConfig?: any
    isActive?: boolean
    priority?: string
    category?: string
    description?: string
    // {{ AURA-X: Add - 添加direction字段支持. Approval: 寸止(ID:1738157400). }}
    direction?: string
    relatedHabits?: string[]
  }): Promise<DatabaseResult<any>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      const metric = await this.prisma.areaMetric.create({
        data: {
          areaId: data.areaId,
          name: data.name,
          value: data.value,
          target: data.target,
          unit: data.unit,
          frequency: data.frequency,
          // {{ AURA-X: Add - 包含新字段的创建逻辑. Approval: 寸止(ID:1738157400). }}
          trackingType: data.trackingType || 'metric',
          habitConfig: data.habitConfig,
          standardConfig: data.standardConfig,
          isActive: data.isActive ?? true,
          priority: data.priority,
          category: data.category,
          description: data.description,
          // {{ AURA-X: Add - 包含direction字段. Approval: 寸止(ID:1738157400). }}
          direction: data.direction || 'increase',
          relatedHabits: data.relatedHabits && data.relatedHabits.length > 0 ? data.relatedHabits : undefined
        }
      })

      return {
        success: true,
        data: metric
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create area metric'
      }
    }
  }

  async getAreaMetrics(areaId: string): Promise<DatabaseResult<any[]>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      const metrics = await this.prisma.areaMetric.findMany({
        where: { areaId },
        orderBy: { updatedAt: 'desc' }
      })

      return {
        success: true,
        data: metrics
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch area metrics'
      }
    }
  }

  async updateAreaMetric(data: {
    id: string
    updates: {
      name?: string
      value?: string
      target?: string
      unit?: string
      frequency?: string
      // {{ AURA-X: Add - 扩展更新参数支持新字段. Approval: 寸止(ID:1738157400). }}
      trackingType?: string
      habitConfig?: any
      standardConfig?: any
      isActive?: boolean
      priority?: string
      category?: string
      description?: string
      // {{ AURA-X: Add - 添加direction字段支持. Approval: 寸止(ID:1738157400). }}
      direction?: string
      relatedHabits?: string[]
    }
  }): Promise<DatabaseResult<any>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      const metric = await this.prisma.areaMetric.update({
        where: { id: data.id },
        data: {
          ...data.updates,
          updatedAt: new Date()
        }
      })

      return {
        success: true,
        data: metric
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update area metric'
      }
    }
  }

  async deleteAreaMetric(id: string): Promise<DatabaseResult<void>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      await this.prisma.areaMetric.delete({
        where: { id }
      })

      return {
        success: true
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete area metric'
      }
    }
  }

  /**
   * Area Metric Record operations
   */
  async createAreaMetricRecord(data: {
    metricId: string
    value: string
    note?: string
    recordedAt?: Date
    // {{ AURA-X: Add - 扩展记录创建参数支持新字段. Approval: 寸止(ID:1738157400). }}
    mood?: string
    energy?: string
    context?: any
    tags?: string[]
    quality?: string
    duration?: number
    difficulty?: string
  }): Promise<DatabaseResult<any>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      const record = await this.prisma.areaMetricRecord.create({
        data: {
          metricId: data.metricId,
          value: data.value,
          note: data.note,
          recordedAt: data.recordedAt || new Date(),
          // {{ AURA-X: Add - 包含新字段的记录创建逻辑. Approval: 寸止(ID:1738157400). }}
          mood: data.mood,
          energy: data.energy,
          context: data.context,
          tags: data.tags || undefined,
          quality: data.quality,
          duration: data.duration,
          difficulty: data.difficulty
        }
      })

      // 同时更新AreaMetric的当前值
      await this.prisma.areaMetric.update({
        where: { id: data.metricId },
        data: {
          value: data.value,
          updatedAt: new Date()
        }
      })

      return {
        success: true,
        data: record
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create area metric record'
      }
    }
  }

  async getAreaMetricRecords(metricId: string, limit?: number): Promise<DatabaseResult<any[]>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      const records = await this.prisma.areaMetricRecord.findMany({
        where: { metricId },
        orderBy: { recordedAt: 'desc' },
        take: limit || 50
      })

      return {
        success: true,
        data: records
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch area metric records'
      }
    }
  }

  async updateAreaMetricRecord(data: {
    id: string
    updates: {
      value?: string
      note?: string
      recordedAt?: Date
    }
  }): Promise<DatabaseResult<any>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      const record = await this.prisma.areaMetricRecord.update({
        where: { id: data.id },
        data: data.updates
      })

      return {
        success: true,
        data: record
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update area metric record'
      }
    }
  }

  async deleteAreaMetricRecord(id: string): Promise<DatabaseResult<void>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      await this.prisma.areaMetricRecord.delete({
        where: { id }
      })

      return {
        success: true
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete area metric record'
      }
    }
  }

  /**
   * Deliverable operations
   */
  async createDeliverable(data: {
    projectId: string
    title: string
    description?: string
    type?: string
    status?: string
    priority?: string
    plannedDate?: Date
    deadline?: Date
    content?: string
    attachments?: any
    metrics?: any
  }): Promise<DatabaseResult<any>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      const deliverable = await this.prisma.deliverable.create({
        data: {
          projectId: data.projectId,
          title: data.title,
          description: data.description,
          type: data.type || 'document',
          status: data.status || 'planned',
          priority: data.priority,
          plannedDate: data.plannedDate,
          deadline: data.deadline,
          content: data.content,
          attachments: data.attachments,
          metrics: data.metrics
        },
        include: {
          project: true,
          tasks: true,
          resources: true
        }
      })

      return {
        success: true,
        data: deliverable
      }
    } catch (error) {
      console.error('Failed to create deliverable:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create deliverable'
      }
    }
  }

  async getProjectDeliverables(projectId: string): Promise<DatabaseResult<any[]>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      const deliverables = await this.prisma.deliverable.findMany({
        where: {
          projectId: projectId
        },
        include: {
          project: true,
          tasks: true,
          resources: true
        },
        orderBy: [
          { status: 'asc' },
          { priority: 'desc' },
          { createdAt: 'desc' }
        ]
      })

      return {
        success: true,
        data: deliverables
      }
    } catch (error) {
      console.error('Failed to get project deliverables:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get project deliverables'
      }
    }
  }

  async updateDeliverable(data: {
    id: string
    updates: {
      title?: string
      description?: string
      type?: string
      status?: string
      priority?: string
      plannedDate?: Date
      actualDate?: Date
      deadline?: Date
      content?: string
      attachments?: any
      metrics?: any
    }
  }): Promise<DatabaseResult<any>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      const deliverable = await this.prisma.deliverable.update({
        where: { id: data.id },
        data: {
          ...data.updates,
          updatedAt: new Date()
        },
        include: {
          project: true,
          tasks: true,
          resources: true
        }
      })

      return {
        success: true,
        data: deliverable
      }
    } catch (error) {
      console.error('Failed to update deliverable:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update deliverable'
      }
    }
  }

  async deleteDeliverable(id: string): Promise<DatabaseResult<void>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      await this.prisma.deliverable.delete({
        where: { id }
      })

      return {
        success: true
      }
    } catch (error) {
      console.error('Failed to delete deliverable:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete deliverable'
      }
    }
  }

  /**
   * Resource linking operations
   */
  async getProjectResources(projectId: string): Promise<DatabaseResult<any[]>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      const resources = await this.prisma.resourceLink.findMany({
        where: {
          projectId: projectId
        },
        orderBy: {
          title: 'asc'
        }
      })

      return {
        success: true,
        data: resources
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get project resources'
      }
    }
  }

  async linkResourceToProject(data: {
    resourceId: string
    projectId: string
  }): Promise<DatabaseResult<any>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      // Update the resource to link it to the project
      const resource = await this.prisma.resourceLink.update({
        where: { id: data.resourceId },
        data: { projectId: data.projectId }
      })

      return {
        success: true,
        data: resource
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to link resource to project'
      }
    }
  }

  async unlinkResourceFromProject(resourceId: string, projectId: string): Promise<DatabaseResult<void>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      // Remove the project link from the resource
      await this.prisma.resourceLink.update({
        where: {
          id: resourceId,
          projectId: projectId
        },
        data: { projectId: null }
      })

      return {
        success: true
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to unlink resource from project'
      }
    }
  }

  async getAreaResources(areaId: string): Promise<DatabaseResult<any[]>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      const resources = await this.prisma.resourceLink.findMany({
        where: { areaId },
        orderBy: { id: 'desc' }
      })

      return {
        success: true,
        data: resources
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get area resources'
      }
    }
  }

  /**
   * Get all projects and areas that reference a specific resource
   */
  async getResourceReferences(resourcePath: string): Promise<DatabaseResult<{
    projects: any[]
    areas: any[]
  }>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      console.log('🔍 [getResourceReferences] 查询路径:', resourcePath)

      // 先查询所有ResourceLink记录用于调试
      const allResourceLinks = await this.prisma.resourceLink.findMany({
        select: {
          id: true,
          resourcePath: true,
          title: true,
          projectId: true,
          areaId: true
        }
      })

      console.log('📋 [getResourceReferences] 数据库中所有ResourceLink记录:', allResourceLinks)

      const resourceLinks = await this.prisma.resourceLink.findMany({
        where: { resourcePath },
        include: {
          project: true,
          area: true
        }
      })

      console.log('🎯 [getResourceReferences] 匹配的ResourceLink记录:', resourceLinks)

      const projects = resourceLinks
        .filter(link => link.project)
        .map(link => link.project)

      const areas = resourceLinks
        .filter(link => link.area)
        .map(link => link.area)

      console.log('✅ [getResourceReferences] 查询结果:', {
        查询路径: resourcePath,
        匹配记录数: resourceLinks.length,
        项目数: projects.length,
        领域数: areas.length
      })

      return {
        success: true,
        data: { projects, areas }
      }
    } catch (error) {
      console.error('❌ [getResourceReferences] 查询失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get resource references'
      }
    }
  }

  async unlinkResourceFromArea(resourceId: string, areaId: string): Promise<DatabaseResult<void>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      // Remove the area link from the resource
      await this.prisma.resourceLink.update({
        where: {
          id: resourceId,
          areaId: areaId
        },
        data: { areaId: null }
      })

      return {
        success: true
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to unlink resource from area'
      }
    }
  }

  // Habit management methods
  async getHabitsByArea(areaId: string): Promise<DatabaseResult<any[]>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      const habits = await this.prisma.habit.findMany({
        where: { areaId },
        orderBy: { createdAt: 'desc' }
      })

      return {
        success: true,
        data: habits
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch habits'
      }
    }
  }

  async createHabit(data: {
    name: string;
    areaId: string;
    description?: string;
    frequency?: string;
    target?: number;
  }): Promise<DatabaseResult<any>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }



      const habit = await this.prisma.habit.create({
        data: {
          name: data.name,
          areaId: data.areaId,
          frequency: data.frequency || 'daily',
          target: data.target || 1 // 使用传入的目标值，默认为1
        }
      })

      return {
        success: true,
        data: habit
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create habit'
      }
    }
  }

  async updateHabit(data: {
    id: string
    updates: {
      name?: string
      description?: string
      frequency?: string
      target?: number
    }
  }): Promise<DatabaseResult<any>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }



      const habit = await this.prisma.habit.update({
        where: { id: data.id },
        data: data.updates
      })

      return {
        success: true,
        data: habit
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update habit'
      }
    }
  }

  async deleteHabit(id: string): Promise<DatabaseResult<void>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }



      await this.prisma.habit.delete({
        where: { id }
      })

      return {
        success: true
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete habit'
      }
    }
  }

  /**
   * Habit Record operations
   */
  async createHabitRecord(data: {
    habitId: string
    date: Date
    completed?: boolean
    value?: number
    note?: string
  }): Promise<DatabaseResult<any>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      // 检查习惯是否存在
      const habitExists = await this.prisma.habit.findUnique({
        where: { id: data.habitId }
      })

      if (!habitExists) {
        throw new Error(`Habit with ID ${data.habitId} does not exist`)
      }

      const record = await this.prisma.habitRecord.create({
        data: {
          habitId: data.habitId,
          date: data.date,
          completed: data.completed ?? true,
          value: data.value,
          note: data.note
        }
      })

      return {
        success: true,
        data: record
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create habit record'
      }
    }
  }

  async updateHabitRecord(data: {
    id: string
    updates: {
      completed?: boolean
      value?: number
      note?: string
    }
  }): Promise<DatabaseResult<any>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      const record = await this.prisma.habitRecord.update({
        where: { id: data.id },
        data: data.updates
      })

      return {
        success: true,
        data: record
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update habit record'
      }
    }
  }

  async getHabitRecords(habitId: string): Promise<DatabaseResult<any[]>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      const records = await this.prisma.habitRecord.findMany({
        where: { habitId },
        orderBy: { date: 'desc' }
      })

      return {
        success: true,
        data: records
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch habit records'
      }
    }
  }

  async deleteHabitRecord(id: string): Promise<DatabaseResult<void>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      await this.prisma.habitRecord.delete({
        where: { id }
      })

      return {
        success: true
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete habit record'
      }
    }
  }

  // {{ AURA-X: Add - 定期维护任务CRUD操作. Approval: 寸止(ID:1738157400). }}
  /**
   * Recurring Task operations
   */
  async createRecurringTask(data: {
    title: string
    description?: string
    repeatRule: string
    repeatInterval?: number
    specificDay?: number
    nextDueDate: Date
    areaId: string
  }): Promise<DatabaseResult<any>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      const task = await this.prisma.recurringTask.create({
        data: {
          title: data.title,
          description: data.description,
          repeatRule: data.repeatRule,
          repeatInterval: data.repeatInterval || 1,
          specificDay: data.specificDay,
          nextDueDate: data.nextDueDate,
          areaId: data.areaId
        }
      })

      return {
        success: true,
        data: task
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create recurring task'
      }
    }
  }

  async getRecurringTasks(areaId: string): Promise<DatabaseResult<any[]>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      const tasks = await this.prisma.recurringTask.findMany({
        where: { areaId },
        orderBy: { nextDueDate: 'asc' }
      })

      return {
        success: true,
        data: tasks
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get recurring tasks'
      }
    }
  }

  async updateRecurringTask(data: {
    id: string
    updates: {
      title?: string
      description?: string
      repeatRule?: string
      repeatInterval?: number
      specificDay?: number
      nextDueDate?: Date
      lastCompletedDate?: Date
    }
  }): Promise<DatabaseResult<any>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      const task = await this.prisma.recurringTask.update({
        where: { id: data.id },
        data: data.updates
      })

      return {
        success: true,
        data: task
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update recurring task'
      }
    }
  }

  async deleteRecurringTask(id: string): Promise<DatabaseResult<void>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      await this.prisma.recurringTask.delete({
        where: { id }
      })

      return {
        success: true
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete recurring task'
      }
    }
  }

  /**
   * Checklist operations
   */
  async createChecklist(data: {
    name: string
    template: any[]
    areaId: string
  }): Promise<DatabaseResult<any>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      const checklist = await this.prisma.checklist.create({
        data: {
          name: data.name,
          template: data.template,
          areaId: data.areaId
        }
      })

      return {
        success: true,
        data: checklist
      }
    } catch (error) {
      console.error('Failed to create checklist:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create checklist'
      }
    }
  }

  async getChecklists(areaId?: string): Promise<DatabaseResult<any[]>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      const where = areaId ? { areaId } : {}
      const checklists = await this.prisma.checklist.findMany({
        where,
        orderBy: { createdAt: 'desc' }
      })

      return {
        success: true,
        data: checklists
      }
    } catch (error) {
      console.error('Failed to get checklists:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get checklists'
      }
    }
  }

  async updateChecklist(id: string, updates: {
    name?: string
    template?: any[]
  }): Promise<DatabaseResult<any>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      const checklist = await this.prisma.checklist.update({
        where: { id },
        data: updates
      })

      return {
        success: true,
        data: checklist
      }
    } catch (error) {
      console.error('Failed to update checklist:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update checklist'
      }
    }
  }

  async deleteChecklist(id: string): Promise<DatabaseResult<void>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      await this.prisma.checklist.delete({
        where: { id }
      })

      return {
        success: true
      }
    } catch (error) {
      console.error('Failed to delete checklist:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete checklist'
      }
    }
  }

  /**
   * Checklist Instance operations
   */
  async createChecklistInstance(data: {
    checklistId: string
    status: any[]
    templateSnapshot?: {
      name: string
      createdAt: Date
    }
  }): Promise<DatabaseResult<any>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      // 将templateSnapshot合并到status中，因为数据库schema只有status字段
      const statusWithSnapshot = {
        items: data.status,
        templateSnapshot: data.templateSnapshot
      }

      const instance = await this.prisma.checklistInstance.create({
        data: {
          checklistId: data.checklistId,
          status: statusWithSnapshot
        }
      })

      return {
        success: true,
        data: instance
      }
    } catch (error) {
      console.error('Failed to create checklist instance:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create checklist instance'
      }
    }
  }

  async getChecklistInstances(areaId?: string): Promise<DatabaseResult<any[]>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      const where = areaId ? {
        checklist: {
          areaId: areaId
        }
      } : {}

      const instances = await this.prisma.checklistInstance.findMany({
        where,
        include: {
          checklist: true
        },
        orderBy: { createdAt: 'desc' }
      })

      return {
        success: true,
        data: instances
      }
    } catch (error) {
      console.error('Failed to get checklist instances:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get checklist instances'
      }
    }
  }

  async updateChecklistInstance(id: string, updates: {
    status?: any[]
    completedAt?: Date
    templateSnapshot?: {
      name: string
      createdAt: Date
    }
  }): Promise<DatabaseResult<any>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      // 如果有status或templateSnapshot更新，需要合并
      let statusData = undefined
      if (updates.status || updates.templateSnapshot) {
        // 先获取当前数据
        const current = await this.prisma.checklistInstance.findUnique({
          where: { id }
        })

        if (current) {
          const currentStatus = current.status as any
          statusData = {
            items: updates.status || currentStatus.items,
            templateSnapshot: updates.templateSnapshot || currentStatus.templateSnapshot
          }
        }
      }

      const updateData: any = {}
      if (statusData) updateData.status = statusData
      if (updates.completedAt !== undefined) updateData.completedAt = updates.completedAt

      const instance = await this.prisma.checklistInstance.update({
        where: { id },
        data: updateData
      })

      return {
        success: true,
        data: instance
      }
    } catch (error) {
      console.error('Failed to update checklist instance:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update checklist instance'
      }
    }
  }

  async deleteChecklistInstance(id: string): Promise<DatabaseResult<void>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      await this.prisma.checklistInstance.delete({
        where: { id }
      })

      return {
        success: true
      }
    } catch (error) {
      console.error('Failed to delete checklist instance:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete checklist instance'
      }
    }
  }

  /**
   * Review operations
   */
  async createReview(data: {
    type: string
    period: string
    title?: string
    content?: any
    summary?: string
    insights?: any
    actionItems?: any
    templateId?: string
    status?: string
  }): Promise<DatabaseResult<any>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      const review = await this.prisma.review.create({
        data: {
          type: data.type,
          period: data.period,
          title: data.title,
          content: data.content || {},
          summary: data.summary,
          insights: data.insights,
          actionItems: data.actionItems,
          templateId: data.templateId,
          status: data.status || 'draft'
        },
        include: {
          template: true
        }
      })

      return {
        success: true,
        data: review
      }
    } catch (error) {
      console.error('Failed to create review:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create review'
      }
    }
  }

  async getReviews(filters?: {
    type?: string
    status?: string
    limit?: number
    offset?: number
  }): Promise<DatabaseResult<any[]>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      const where: any = {}
      if (filters?.type) where.type = filters.type
      if (filters?.status) where.status = filters.status

      const reviews = await this.prisma.review.findMany({
        where,
        include: {
          template: true
        },
        orderBy: { createdAt: 'desc' },
        take: filters?.limit || 50,
        skip: filters?.offset || 0
      })

      return {
        success: true,
        data: reviews
      }
    } catch (error) {
      console.error('Failed to get reviews:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get reviews'
      }
    }
  }

  async getReviewById(id: string): Promise<DatabaseResult<any>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      const review = await this.prisma.review.findUnique({
        where: { id },
        include: {
          template: true
        }
      })

      if (!review) {
        return {
          success: false,
          error: 'Review not found'
        }
      }

      return {
        success: true,
        data: review
      }
    } catch (error) {
      console.error('Failed to get review:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get review'
      }
    }
  }

  async updateReview(data: {
    id: string
    updates: {
      title?: string
      content?: any
      summary?: string
      insights?: any
      actionItems?: any
      status?: string
      completedAt?: Date
    }
  }): Promise<DatabaseResult<any>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      const review = await this.prisma.review.update({
        where: { id: data.id },
        data: {
          ...data.updates,
          updatedAt: new Date()
        },
        include: {
          template: true
        }
      })

      return {
        success: true,
        data: review
      }
    } catch (error) {
      console.error('Failed to update review:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update review'
      }
    }
  }

  async deleteReview(id: string): Promise<DatabaseResult<void>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      await this.prisma.review.delete({
        where: { id }
      })

      return {
        success: true
      }
    } catch (error) {
      console.error('Failed to delete review:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete review'
      }
    }
  }

  /**
   * ReviewTemplate operations
   */
  async createReviewTemplate(data: {
    name: string
    description?: string
    type: string
    structure: any
    isDefault?: boolean
    isSystem?: boolean
  }): Promise<DatabaseResult<any>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      const template = await this.prisma.reviewTemplate.create({
        data: {
          name: data.name,
          description: data.description,
          type: data.type,
          structure: data.structure,
          isDefault: data.isDefault || false,
          isSystem: data.isSystem || false
        }
      })

      return {
        success: true,
        data: template
      }
    } catch (error) {
      console.error('Failed to create review template:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create review template'
      }
    }
  }

  async getReviewTemplates(type?: string): Promise<DatabaseResult<any[]>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      const where = type ? { type } : {}
      const templates = await this.prisma.reviewTemplate.findMany({
        where,
        orderBy: [
          { isDefault: 'desc' },
          { isSystem: 'desc' },
          { createdAt: 'desc' }
        ]
      })

      return {
        success: true,
        data: templates
      }
    } catch (error) {
      console.error('Failed to get review templates:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get review templates'
      }
    }
  }

  async getReviewTemplateById(id: string): Promise<DatabaseResult<any>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      const template = await this.prisma.reviewTemplate.findUnique({
        where: { id }
      })

      if (!template) {
        return {
          success: false,
          error: 'Review template not found'
        }
      }

      return {
        success: true,
        data: template
      }
    } catch (error) {
      console.error('Failed to get review template:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get review template'
      }
    }
  }

  async updateReviewTemplate(data: {
    id: string
    updates: {
      name?: string
      description?: string
      structure?: any
      isDefault?: boolean
    }
  }): Promise<DatabaseResult<any>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      const template = await this.prisma.reviewTemplate.update({
        where: { id: data.id },
        data: {
          ...data.updates,
          updatedAt: new Date()
        }
      })

      return {
        success: true,
        data: template
      }
    } catch (error) {
      console.error('Failed to update review template:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update review template'
      }
    }
  }

  async deleteReviewTemplate(id: string): Promise<DatabaseResult<void>> {
    try {
      if (!this.prisma) {
        throw new Error('Database not initialized')
      }

      // 检查是否为系统模板
      const template = await this.prisma.reviewTemplate.findUnique({
        where: { id }
      })

      if (template?.isSystem) {
        return {
          success: false,
          error: 'Cannot delete system template'
        }
      }

      await this.prisma.reviewTemplate.delete({
        where: { id }
      })

      return {
        success: true
      }
    } catch (error) {
      console.error('Failed to delete review template:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete review template'
      }
    }
  }

  /**
   * Get aggregated data for review
   */
  async getReviewAggregatedData(type: string, period: string): Promise<DatabaseResult<any>> {
    try {
      if (!this.dataAggregator) {
        throw new Error('Data aggregator not initialized')
      }

      const aggregatedData = await this.dataAggregator.aggregateReviewData(type, period)

      return {
        success: true,
        data: aggregatedData
      }
    } catch (error) {
      console.error('Failed to get aggregated review data:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get aggregated review data'
      }
    }
  }

  /**
   * Get intelligent analysis for review
   */
  async getReviewAnalysis(type: string, period: string): Promise<DatabaseResult<any>> {
    try {
      if (!this.reviewAnalyzer) {
        throw new Error('Review analyzer not initialized')
      }

      const analysisResult = await this.reviewAnalyzer.analyzeReview(type, period)

      return {
        success: true,
        data: analysisResult
      }
    } catch (error) {
      console.error('Failed to get review analysis:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get review analysis'
      }
    }
  }
}

// Export singleton instance
export const databaseService = new DatabaseService()
export default databaseService
