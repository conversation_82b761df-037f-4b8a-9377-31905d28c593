# PaoLife electron-builder 打包指南

## 概述

本指南专门针对PaoLife项目使用electron-builder进行打包的详细流程，包括配置优化、构建命令和常见问题解决。

## 1. 当前配置分析

### 1.1 package.json 构建脚本
```json
{
  "scripts": {
    "build": "npm run typecheck && prisma generate && electron-vite build",
    "build:unpack": "npm run build && electron-builder --dir",
    "build:win": "npm run build && electron-builder --win",
    "postinstall": "electron-builder install-app-deps && prisma generate"
  }
}
```

### 1.2 electron-builder.yml 配置文件
```yaml
appId: com.electron.app
productName: paolife
directories:
  buildResources: build

# Windows特定配置
win:
  executableName: paolife

# NSIS安装程序配置
nsis:
  artifactName: ${name}-${version}-setup.${ext}
  shortcutName: ${productName}
  uninstallDisplayName: ${productName}
  createDesktopShortcut: always

# 发布配置
publish:
  provider: generic
  url: https://example.com/auto-updates

# Electron下载镜像
electronDownload:
  mirror: https://npmmirror.com/mirrors/electron/
```

## 2. 打包前准备

### 2.1 环境检查
```bash
# 检查Node.js版本
node --version  # 应该是18.x或更高

# 检查pnpm版本
pnpm --version

# 检查项目依赖
pnpm install
```

### 2.2 构建资源准备
确保以下文件存在于 `build/` 目录：
```
build/
├── icon.png      # 主图标 (1024x1024)
├── icon.ico      # Windows图标
├── icon.icns     # macOS图标 (如果需要)
└── entitlements.mac.plist
```

### 2.3 版本号更新
```json
// package.json
{
  "version": "1.0.0",  // 更新版本号
  "description": "PaoLife - 个人知识管理系统"
}
```

## 3. 基础打包命令

### 3.1 Windows平台打包
```bash
# 标准Windows安装包
pnpm run build:win

# 等价命令
pnpm run build && electron-builder --win
```

### 3.2 未打包版本（用于测试）
```bash
# 构建但不打包，输出到dist目录
pnpm run build:unpack

# 等价命令
pnpm run build && electron-builder --dir
```

### 3.3 特定架构打包
```bash
# 64位Windows
npx electron-builder --win --x64

# 32位Windows
npx electron-builder --win --ia32

# 同时构建多个架构
npx electron-builder --win --x64 --ia32
```

## 4. 高级打包配置

### 4.1 多目标打包
```yaml
# electron-builder.yml
win:
  target:
    - target: nsis      # NSIS安装程序
      arch: [x64, ia32]
    - target: portable  # 便携版
      arch: [x64]
    - target: msi       # MSI安装包
      arch: [x64]
```

### 4.2 便携版配置

```yaml
# electron-builder.yml
portable:
  artifactName: ${name}-${version}-portable.${ext}
  requestedExecutionLevel: asInvoker
  unpackDirName: ${productName}
```

### 4.3 NSIS安装程序优化
```yaml
nsis:
  oneClick: false                    # 允许用户选择安装路径
  allowElevation: true              # 允许提升权限
  allowToChangeInstallationDirectory: true
  installerIcon: build/icon.ico     # 安装程序图标
  uninstallerIcon: build/icon.ico   # 卸载程序图标
  createDesktopShortcut: always     # 创建桌面快捷方式
  createStartMenuShortcut: true     # 创建开始菜单快捷方式
  shortcutName: PaoLife             # 快捷方式名称
  differentialPackage: true         # 启用增量更新
```

## 5. 构建优化

### 5.1 文件排除配置
```yaml
# electron-builder.yml
files:
  - '!**/.vscode/*'
  - '!src/*'
  - '!electron.vite.config.{js,ts,mjs,cjs}'
  - '!{.eslintcache,eslint.config.mjs,.prettierignore,.prettierrc.yaml}'
  - '!{.env,.env.*,.npmrc,pnpm-lock.yaml}'
  - '!{tsconfig.json,tsconfig.node.json,tsconfig.web.json}'
  - '!docs/*'
  - '!README.md'
  - '!.git/*'
```

### 5.2 压缩优化
```yaml
compression: maximum  # 最大压缩
nsis:
  differentialPackage: true  # 启用增量更新包
```

### 5.3 构建缓存
```bash
# 设置缓存目录环境变量
set ELECTRON_CACHE=C:\electron-cache
set ELECTRON_BUILDER_CACHE=C:\electron-builder-cache

# 或在.env文件中设置
ELECTRON_CACHE=./cache/electron
ELECTRON_BUILDER_CACHE=./cache/electron-builder
```

## 6. 打包命令详解

### 6.1 完整构建流程
```bash
# 1. 清理之前的构建
rm -rf dist out

# 2. 安装依赖
pnpm install

# 3. 类型检查
pnpm run typecheck

# 4. 生成Prisma客户端
npx prisma generate

# 5. 构建应用
pnpm run build

# 6. 打包Windows版本
electron-builder --win
```

### 6.2 调试构建
```bash
# 详细输出
electron-builder --win --verbose

# 跳过代码签名
electron-builder --win --publish=never

# 构建到指定目录
electron-builder --win --dir

# 不压缩（加快构建速度）
electron-builder --win --config.compression=store
```

### 6.3 发布构建
```bash
# 构建并发布
electron-builder --win --publish=always

# 构建草稿版本
electron-builder --win --publish=onTagOrDraft
```

## 7. 输出文件说明

### 7.1 构建输出目录
```
dist/
├── paolife-1.0.0-setup.exe          # NSIS安装程序
├── paolife-1.0.0-setup.exe.blockmap # 增量更新映射
├── paolife-1.0.0-portable.exe       # 便携版（如果配置）
├── paolife-1.0.0.msi                # MSI安装包（如果配置）
├── latest.yml                       # 更新元数据
└── builder-debug.yml                # 构建调试信息
```

### 7.2 文件用途说明
- **setup.exe**: 标准安装程序，用于分发给用户
- **portable.exe**: 便携版，无需安装直接运行
- **blockmap**: 用于增量更新的文件映射
- **latest.yml**: 自动更新系统的元数据文件

## 8. 代码签名

### 8.1 证书配置
```bash
# 环境变量方式
set CSC_LINK=path/to/certificate.p12
set CSC_KEY_PASSWORD=your_password

# 或在electron-builder.yml中配置
win:
  certificateFile: certificate.p12
  certificatePassword: ${env.CSC_KEY_PASSWORD}
  signingHashAlgorithms: [sha256]
```

### 8.2 签名验证
```bash
# 验证签名
signtool verify /pa /v "dist/paolife-1.0.0-setup.exe"
```

## 9. 常见问题解决

### 9.1 构建失败
```bash
# 错误: Cannot find module 'electron'
# 解决: 重新安装依赖
rm -rf node_modules
pnpm install

# 错误: EPERM: operation not permitted
# 解决: 以管理员身份运行
```

### 9.2 图标问题
```bash
# 图标不显示
# 检查图标文件是否存在
ls build/icon.ico

# 图标格式错误
# 重新生成图标文件
magick icon.png -define icon:auto-resize=256,64,48,32,16 icon.ico
```

### 9.3 依赖问题
```bash
# 原生模块编译失败
# 安装构建工具
npm install -g windows-build-tools

# Prisma相关错误
# 重新生成客户端
npx prisma generate
```

## 10. 性能优化建议

### 10.1 构建速度优化
```yaml
# electron-builder.yml
buildDependenciesFromSource: false
nodeGypRebuild: false
npmRebuild: false
```

### 10.2 包大小优化
```yaml
# 排除开发依赖
includeSubNodeModules: false

# 压缩配置
compression: maximum
```

### 10.3 并行构建
```bash
# 使用多核构建
electron-builder --win --parallel
```

## 11. 自动化脚本

### 11.1 批处理脚本 (build.bat)
```batch
@echo off
echo 开始构建PaoLife...

echo 1. 清理构建目录
rmdir /s /q dist 2>nul
rmdir /s /q out 2>nul

echo 2. 安装依赖
call pnpm install

echo 3. 类型检查
call pnpm run typecheck
if errorlevel 1 (
    echo 类型检查失败！
    pause
    exit /b 1
)

echo 4. 构建应用
call pnpm run build:win
if errorlevel 1 (
    echo 构建失败！
    pause
    exit /b 1
)

echo 构建完成！输出文件在 dist 目录
pause
```

### 11.2 PowerShell脚本 (build.ps1)
```powershell
Write-Host "开始构建PaoLife..." -ForegroundColor Green

# 清理构建目录
Remove-Item -Path "dist" -Recurse -Force -ErrorAction SilentlyContinue
Remove-Item -Path "out" -Recurse -Force -ErrorAction SilentlyContinue

# 安装依赖
Write-Host "安装依赖..." -ForegroundColor Yellow
pnpm install

# 类型检查
Write-Host "类型检查..." -ForegroundColor Yellow
pnpm run typecheck
if ($LASTEXITCODE -ne 0) {
    Write-Host "类型检查失败！" -ForegroundColor Red
    exit 1
}

# 构建应用
Write-Host "构建应用..." -ForegroundColor Yellow
pnpm run build:win
if ($LASTEXITCODE -ne 0) {
    Write-Host "构建失败！" -ForegroundColor Red
    exit 1
}

Write-Host "构建完成！输出文件在 dist 目录" -ForegroundColor Green
```

## 12. 验证清单

### 12.1 构建前检查
- [ ] 版本号已更新
- [ ] 图标文件存在且格式正确
- [ ] 依赖项已安装
- [ ] 类型检查通过
- [ ] 证书配置正确（如果需要签名）

### 12.2 构建后验证
- [ ] 安装程序可以正常运行
- [ ] 应用图标显示正确
- [ ] 快捷方式创建成功
- [ ] 卸载程序工作正常
- [ ] 便携版可以独立运行

---

## 总结

使用electron-builder打包PaoLife应用的关键步骤：

1. **准备环境**: 确保Node.js、pnpm和构建工具已安装
2. **配置文件**: 检查electron-builder.yml配置
3. **构建资源**: 准备图标文件和其他资源
4. **执行构建**: 运行 `pnpm run build:win`
5. **验证结果**: 测试生成的安装包

遵循本指南可以确保成功构建出高质量的Windows安装包。
